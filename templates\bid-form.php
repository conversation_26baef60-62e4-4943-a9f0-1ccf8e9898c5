<?php
/**
 * Template for bid submission form
 *
 * @package USF_Auction
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Check if user is logged in
if (!is_user_logged_in()) {
?>
<div class="usf-bid-form-container">
    <h3><PERSON>gin Required</h3>
    <p>You must be logged in to place a bid on this auction.</p>
    <div class="usf-login-actions">
        <a href="<?php echo esc_url(wp_login_url(get_permalink())); ?>" class="usf-btn usf-btn-primary">Login</a>
        <a href="<?php echo esc_url(wp_registration_url()); ?>" class="usf-btn usf-btn-secondary">Register</a>
    </div>
</div>
<?php
    return;
}

// Get current user data
$current_user = wp_get_current_user();

// Check user's bid status for this lot
$user_bid_status = USF_Database::get_user_bid_status($auction->lot_id, $current_user->ID);
$can_bid = USF_Database::user_can_bid($auction->lot_id, $current_user->ID);

// Check if auction is still active
$closing_time = strtotime($auction->closing_time);
$now = current_time('timestamp');
$is_auction_active = $closing_time > $now;
?>

<div class="usf-bid-form-container">
    <?php if ($user_bid_status): ?>
        <?php if ($user_bid_status->status === 'pending'): ?>
            <!-- User has pending bid -->
            <h3>Bid Submitted</h3>
            <div class="usf-bid-status usf-bid-pending">
                <div class="usf-status-icon">⏳</div>
                <div class="usf-status-content">
                    <p><strong>Your bid is under review</strong></p>
                    <div class="usf-bid-details">
                        <p><strong>Bid Amount:</strong> $<?php echo number_format($user_bid_status->bid_amount, 2); ?></p>
                        <p><strong>Submitted:</strong> <?php echo date('M j, Y g:i A', strtotime($user_bid_status->bid_time)); ?></p>
                    </div>
                    <p>You will receive an email notification once a decision has been made on your bid.</p>
                </div>
            </div>
            
        <?php elseif ($user_bid_status->status === 'accepted'): ?>
            <!-- User's bid was accepted -->
            <h3>Congratulations!</h3>
            <div class="usf-bid-status usf-bid-accepted">
                <div class="usf-status-icon">🎉</div>
                <div class="usf-status-content">
                    <p><strong>Your bid has been accepted!</strong></p>
                    <div class="usf-bid-details">
                        <p><strong>Winning Bid:</strong> $<?php echo number_format($user_bid_status->bid_amount, 2); ?></p>
                        <p><strong>Accepted:</strong> <?php echo date('M j, Y g:i A', strtotime($user_bid_status->bid_time)); ?></p>
                    </div>
                    <?php if ($user_bid_status->woocommerce_order_id): ?>
                        <p><a href="<?php echo esc_url(wc_get_order_url($user_bid_status->woocommerce_order_id)); ?>" class="usf-btn usf-btn-primary">Complete Payment</a></p>
                    <?php endif; ?>
                </div>
            </div>
            
        <?php elseif ($user_bid_status->status === 'rejected'): ?>
            <!-- User's bid was rejected - can bid again if auction is active -->
            <h3>Bid Not Accepted</h3>
            <div class="usf-bid-status usf-bid-rejected">
                <div class="usf-status-icon">❌</div>
                <div class="usf-status-content">
                    <p><strong>Your previous bid was not accepted</strong></p>
                    <div class="usf-bid-details">
                        <p><strong>Previous Bid:</strong> $<?php echo number_format($user_bid_status->bid_amount, 2); ?></p>
                        <?php if (!empty($user_bid_status->admin_notes)): ?>
                            <p><strong>Note:</strong> <?php echo esc_html($user_bid_status->admin_notes); ?></p>
                        <?php endif; ?>
                    </div>
                    <?php if ($is_auction_active): ?>
                        <p><strong>You can submit a new bid below.</strong></p>
                    <?php else: ?>
                        <p>This auction has ended and is no longer accepting bids.</p>
                    <?php endif; ?>
                </div>
            </div>
            
            <?php if ($is_auction_active): ?>
                <!-- Show bid form for rejected bids if auction is still active -->
                <div class="usf-new-bid-section">
                    <h4>Submit New Bid</h4>
                    <form class="usf-bid-form" data-lot-id="<?php echo esc_attr($auction->lot_id); ?>">
                        <?php wp_nonce_field('usf_submit_bid', 'usf_bid_nonce'); ?>
                        
                        <div class="usf-form-group">
                            <label for="bid_amount">Bid Amount * (Minimum: $<?php echo number_format($auction->min_offer, 2); ?>)</label>
                            <input type="number" 
                                   id="bid_amount" 
                                   name="bid_amount" 
                                   min="<?php echo esc_attr($auction->min_offer); ?>" 
                                   step="0.01" 
                                   placeholder="<?php echo esc_attr($auction->min_offer); ?>"
                                   required>
                            <small>Enter your bid amount in USD. Must be at least $<?php echo number_format($auction->min_offer, 2); ?>.</small>
                        </div>
                        
                        <div class="usf-form-group">
                            <label class="usf-checkbox-label">
                                <input type="checkbox" id="terms_accepted" name="terms_accepted" required>
                                I agree to the <a href="#usf-terms" onclick="document.querySelector('.usf-auction-terms').scrollIntoView()">terms and conditions</a> *
                            </label>
                        </div>
                        
                        <button type="submit" class="usf-btn usf-btn-primary usf-btn-large">
                            Submit New Bid
                        </button>
                        
                        <div class="usf-bid-messages"></div>
                    </form>
                </div>
            <?php endif; ?>
        <?php endif; ?>
        
    <?php else: ?>
        <!-- User has no bid - show normal bid form -->
        <?php if ($is_auction_active): ?>
            <h3>Place Your Bid</h3>
            
            <form class="usf-bid-form" data-lot-id="<?php echo esc_attr($auction->lot_id); ?>">
                <?php wp_nonce_field('usf_submit_bid', 'usf_bid_nonce'); ?>
                
                <div class="usf-form-group">
                    <label for="bid_amount">Bid Amount * (Minimum: $<?php echo number_format($auction->min_offer, 2); ?>)</label>
                    <input type="number" 
                           id="bid_amount" 
                           name="bid_amount" 
                           min="<?php echo esc_attr($auction->min_offer); ?>" 
                           step="0.01" 
                           placeholder="<?php echo esc_attr($auction->min_offer); ?>"
                           required>
                    <small>Enter your bid amount in USD. Must be at least $<?php echo number_format($auction->min_offer, 2); ?>.</small>
                </div>
                
                <div class="usf-form-group">
                    <label class="usf-checkbox-label">
                        <input type="checkbox" id="terms_accepted" name="terms_accepted" required>
                        I agree to the <a href="#usf-terms" onclick="document.querySelector('.usf-auction-terms').scrollIntoView()">terms and conditions</a> *
                    </label>
                </div>
                
                <button type="submit" class="usf-btn usf-btn-primary usf-btn-large">
                    Submit Bid
                </button>
                
                <div class="usf-bid-messages"></div>
            </form>
        <?php else: ?>
            <h3>Auction Closed</h3>
            <p>This auction has ended and is no longer accepting bids.</p>
        <?php endif; ?>
    <?php endif; ?>
    
    <!-- Bid Information -->
    <div class="usf-bid-info">
        <h4>Important Information</h4>
        <ul>
            <li><strong>Binding Agreement:</strong> Your bid is a binding offer to purchase.</li>
            <li><strong>Payment:</strong> Payment must be completed within 48 hours if your bid is accepted.</li>
            <li><strong>Inspection:</strong> Items are sold as-is. Please review the item details carefully.</li>
            <li><strong>Updates:</strong> You'll receive email notifications about your bid status.</li>
            <?php if ($user_bid_status && $user_bid_status->status === 'rejected' && $is_auction_active): ?>
                <li><strong>Re-bidding:</strong> You can submit a new bid since your previous bid was not accepted.</li>
            <?php endif; ?>
        </ul>
    </div>
    
    <!-- Contact Information -->
    <div class="usf-contact-info">
        <h4>Questions?</h4>
        <p>Contact us for more information about this auction:</p>
        <ul>
            <?php 
            $contact_email = get_option('usf_contact_email', get_option('admin_email'));
            $contact_phone = get_option('usf_contact_phone', '');
            ?>
            <li><strong>Email:</strong> <a href="mailto:<?php echo esc_attr($contact_email); ?>"><?php echo esc_html($contact_email); ?></a></li>
            <?php if ($contact_phone): ?>
                <li><strong>Phone:</strong> <a href="tel:<?php echo esc_attr($contact_phone); ?>"><?php echo esc_html($contact_phone); ?></a></li>
            <?php endif; ?>
        </ul>
    </div>
</div>
