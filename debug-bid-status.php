<?php
/**
 * Debug script to test bid status functionality
 * 
 * Add this to your WordPress site temporarily to debug bid status issues
 * Access via: yoursite.com/debug-bid-status.php
 */

// Load WordPress
require_once('wp-config.php');

// Check if user is logged in
if (!is_user_logged_in()) {
    echo "<h1>Debug: User Not Logged In</h1>";
    echo "<p>Please log in to test bid status functionality.</p>";
    echo "<p><a href='" . wp_login_url() . "'>Login</a></p>";
    exit;
}

$current_user = wp_get_current_user();
echo "<h1>Bid Status Debug</h1>";
echo "<h2>Current User: " . $current_user->display_name . " (ID: " . $current_user->ID . ")</h2>";

// Get some sample auctions
$auctions = USF_Database::get_lots(array('limit' => 5));

if (empty($auctions)) {
    echo "<p><strong>No auctions found in database.</strong></p>";
    exit;
}

echo "<h2>Testing Bid Status Functions</h2>";

foreach ($auctions as $auction) {
    echo "<div style='border: 1px solid #ccc; padding: 15px; margin: 10px 0;'>";
    echo "<h3>Lot #" . $auction->lot_id . " - " . $auction->model . "</h3>";
    
    // Test get_user_bid_status
    $user_bid_status = USF_Database::get_user_bid_status($auction->lot_id, $current_user->ID);
    echo "<p><strong>User Bid Status:</strong> ";
    if ($user_bid_status) {
        echo "Found - Status: " . $user_bid_status->status . ", Amount: $" . number_format($user_bid_status->bid_amount, 2) . ", Time: " . $user_bid_status->bid_time;
    } else {
        echo "No bid found";
    }
    echo "</p>";
    
    // Test user_can_bid
    $can_bid = USF_Database::user_can_bid($auction->lot_id, $current_user->ID);
    echo "<p><strong>Can Bid:</strong> " . ($can_bid ? 'Yes' : 'No') . "</p>";
    
    // Test auction_has_bids
    $has_bids = USF_Database::auction_has_bids($auction->lot_id);
    echo "<p><strong>Auction Has Bids:</strong> " . ($has_bids ? 'Yes' : 'No') . "</p>";
    
    // Test get_auction_display_price
    $display_price = USF_Database::get_auction_display_price($auction->lot_id, $auction->min_offer);
    echo "<p><strong>Display Price:</strong> $" . number_format($display_price, 2) . "</p>";
    
    echo "</div>";
}

// Test get_user_bid_statuses for multiple lots
echo "<h2>Testing Multiple Lot Bid Statuses</h2>";
$lot_ids = wp_list_pluck($auctions, 'lot_id');
$user_bid_statuses = USF_Database::get_user_bid_statuses($lot_ids, $current_user->ID);

echo "<p><strong>Batch Bid Status Results:</strong></p>";
if (empty($user_bid_statuses)) {
    echo "<p>No bid statuses found for current user.</p>";
} else {
    foreach ($user_bid_statuses as $lot_id => $status) {
        echo "<p>Lot #$lot_id: Status = " . $status->status . ", Amount = $" . number_format($status->bid_amount, 2) . "</p>";
    }
}

// Check database tables exist
global $wpdb;
$table_bids = $wpdb->prefix . 'auction_bids';
$table_lots = $wpdb->prefix . 'auction_lots';

echo "<h2>Database Table Check</h2>";
$bids_exist = $wpdb->get_var("SHOW TABLES LIKE '$table_bids'");
$lots_exist = $wpdb->get_var("SHOW TABLES LIKE '$table_lots'");

echo "<p><strong>Bids Table ($table_bids):</strong> " . ($bids_exist ? 'Exists' : 'Missing') . "</p>";
echo "<p><strong>Lots Table ($table_lots):</strong> " . ($lots_exist ? 'Exists' : 'Missing') . "</p>";

// Show sample bids
$sample_bids = $wpdb->get_results("SELECT * FROM $table_bids ORDER BY bid_time DESC LIMIT 5");
echo "<h2>Sample Bids (Latest 5)</h2>";
if (empty($sample_bids)) {
    echo "<p>No bids found in database.</p>";
} else {
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>ID</th><th>Lot ID</th><th>User ID</th><th>Amount</th><th>Status</th><th>Time</th></tr>";
    foreach ($sample_bids as $bid) {
        echo "<tr>";
        echo "<td>" . $bid->id . "</td>";
        echo "<td>" . $bid->lot_id . "</td>";
        echo "<td>" . $bid->user_id . "</td>";
        echo "<td>$" . number_format($bid->bid_amount, 2) . "</td>";
        echo "<td>" . $bid->status . "</td>";
        echo "<td>" . $bid->bid_time . "</td>";
        echo "</tr>";
    }
    echo "</table>";
}

echo "<p><em>Debug complete. Delete this file when done testing.</em></p>";
?>
