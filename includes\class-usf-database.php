<?php

/**
 * The database functionality of the plugin.
 *
 * Handles database table creation, updates, and operations.
 */

class USF_Database {

    /**
     * Create all plugin tables
     */
    public static function create_tables() {
        global $wpdb;

        $charset_collate = $wpdb->get_charset_collate();

        // Table for auction lots
        $table_lots = $wpdb->prefix . 'auction_lots';
        $sql_lots = "CREATE TABLE $table_lots (
            id int(11) NOT NULL AUTO_INCREMENT,
            lot_id varchar(100) NOT NULL,
            source_file varchar(50) NOT NULL,
            auction_house varchar(50) NOT NULL,
            url text,
            model varchar(255),
            memory varchar(100),
            grade varchar(100),
            total_units int(11),
            closing_time datetime,
            min_offer decimal(10,2),
            status varchar(20) DEFAULT 'active',
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY lot_id (lot_id),
            KEY status (status),
            KEY closing_time (closing_time),
            KEY auction_house (auction_house)
        ) $charset_collate;";

        // Table for auction items
        $table_items = $wpdb->prefix . 'auction_items';
        $sql_items = "CREATE TABLE $table_items (
            id int(11) NOT NULL AUTO_INCREMENT,
            lot_id varchar(100) NOT NULL,
            quantity int(11),
            manufacturer varchar(100),
            model varchar(255),
            description text,
            part_number varchar(100),
            category varchar(100),
            grade varchar(100),
            capacity varchar(100),
            color varchar(100),
            carrier varchar(100),
            additional_data longtext,
            PRIMARY KEY (id),
            KEY lot_id (lot_id),
            KEY manufacturer (manufacturer),
            KEY model (model)
        ) $charset_collate;";

        // Table for auction bids
        $table_bids = $wpdb->prefix . 'auction_bids';
        $sql_bids = "CREATE TABLE $table_bids (
            id int(11) NOT NULL AUTO_INCREMENT,
            lot_id varchar(100) NOT NULL,
            user_id int(11),
            user_email varchar(255) NOT NULL,
            user_name varchar(255) NOT NULL,
            user_phone varchar(50),
            bid_amount decimal(10,2) NOT NULL,
            bid_time datetime DEFAULT CURRENT_TIMESTAMP,
            status varchar(20) DEFAULT 'pending',
            woocommerce_order_id int(11),
            admin_notes text,
            PRIMARY KEY (id),
            KEY lot_id (lot_id),
            KEY user_email (user_email),
            KEY status (status),
            KEY bid_time (bid_time),
            KEY woocommerce_order_id (woocommerce_order_id)
        ) $charset_collate;";

        // Table for plugin settings
        $table_settings = $wpdb->prefix . 'auction_settings';
        $sql_settings = "CREATE TABLE $table_settings (
            id int(11) NOT NULL AUTO_INCREMENT,
            setting_name varchar(100) NOT NULL,
            setting_value longtext,
            PRIMARY KEY (id),
            UNIQUE KEY setting_name (setting_name)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        
        dbDelta($sql_lots);
        dbDelta($sql_items);
        dbDelta($sql_bids);
        dbDelta($sql_settings);

        // Insert default settings
        self::insert_default_settings();
    }

    /**
     * Insert default plugin settings
     */
    private static function insert_default_settings() {
        global $wpdb;

        $table_settings = $wpdb->prefix . 'auction_settings';

        $default_settings = array(
            'email_bid_confirmation_subject' => 'Bid submitted for Lot #{lot_id}',
            'email_bid_confirmation_body' => 'Dear {user_name},

Thank you for submitting your bid for Lot #{lot_id}.

Bid Details:
- Lot ID: {lot_id}
- Model: {model}
- Grade: {grade}
- Your Bid: ${bid_amount}
- Minimum Offer: ${min_offer}

Your bid is currently under review. You will receive an email notification once a decision has been made.

Best regards,
Auction Team',
            'email_admin_notification_subject' => 'New bid received for Lot #{lot_id}',
            'email_admin_notification_body' => 'A new bid has been received:

Bidder: {user_name}
Email: {user_email}
Phone: {user_phone}
Lot ID: {lot_id}
Bid Amount: ${bid_amount}
Minimum Offer: ${min_offer}

Please review and take action in the admin panel.',
            'email_bid_acceptance_subject' => 'Congratulations! Your bid was accepted for Lot #{lot_id}',
            'email_bid_acceptance_body' => 'Dear {user_name},

Congratulations! Your bid for Lot #{lot_id} has been accepted.

Winning Bid Details:
- Lot ID: {lot_id}
- Model: {model}
- Grade: {grade}
- Winning Bid: ${bid_amount}

Please complete your payment using the link below:
{order_link}

Best regards,
Auction Team',
            'email_bid_rejection_subject' => 'Bid not accepted for Lot #{lot_id}',
            'email_bid_rejection_body' => 'Dear {user_name},

Thank you for your interest in Lot #{lot_id}. Unfortunately, your bid was not accepted at this time.

{admin_notes}

We encourage you to browse our other available lots and submit new bids.

Best regards,
Auction Team',
            'admin_email' => get_option('admin_email'),
            'company_name' => get_bloginfo('name'),
            'contact_phone' => '',
            'contact_address' => '',
            'single_auction_page' => ''
        );

        foreach ($default_settings as $name => $value) {
            $wpdb->replace(
                $table_settings,
                array(
                    'setting_name' => $name,
                    'setting_value' => $value
                ),
                array('%s', '%s')
            );
        }
    }

    /**
     * Get a setting value
     */
    public static function get_setting($setting_name, $default = '') {
        global $wpdb;

        $table_settings = $wpdb->prefix . 'auction_settings';
        
        $result = $wpdb->get_var($wpdb->prepare(
            "SELECT setting_value FROM $table_settings WHERE setting_name = %s",
            $setting_name
        ));

        return $result !== null ? $result : $default;
    }

    /**
     * Update a setting value
     */
    public static function update_setting($setting_name, $setting_value) {
        global $wpdb;

        $table_settings = $wpdb->prefix . 'auction_settings';
        
        return $wpdb->replace(
            $table_settings,
            array(
                'setting_name' => $setting_name,
                'setting_value' => $setting_value
            ),
            array('%s', '%s')
        );
    }

    /**
     * Get all lots with optional filters
     */
    public static function get_lots($args = array()) {
        global $wpdb;

        $table_lots = $wpdb->prefix . 'auction_lots';
        
        $defaults = array(
            'status' => '',
            'limit' => 20,
            'offset' => 0,
            'orderby' => 'closing_time',
            'order' => 'ASC',
            'search' => '',
            'auction_house' => ''
        );

        $args = wp_parse_args($args, $defaults);

        $where_clauses = array();
        $where_values = array();

        if (!empty($args['status'])) {
            $where_clauses[] = "status = %s";
            $where_values[] = $args['status'];
        }

        if (!empty($args['search'])) {
            $where_clauses[] = "(lot_id LIKE %s OR model LIKE %s OR grade LIKE %s)";
            $search_term = '%' . $wpdb->esc_like($args['search']) . '%';
            $where_values[] = $search_term;
            $where_values[] = $search_term;
            $where_values[] = $search_term;
        }

        if (!empty($args['auction_house'])) {
            $where_clauses[] = "auction_house = %s";
            $where_values[] = $args['auction_house'];
        }

        $where_sql = '';
        if (!empty($where_clauses)) {
            $where_sql = 'WHERE ' . implode(' AND ', $where_clauses);
        }

        $order_sql = sprintf('ORDER BY %s %s', 
            sanitize_sql_orderby($args['orderby']), 
            $args['order'] === 'DESC' ? 'DESC' : 'ASC'
        );

        $limit_sql = '';
        if ($args['limit'] > 0) {
            $limit_sql = $wpdb->prepare('LIMIT %d OFFSET %d', $args['limit'], $args['offset']);
        }

        $sql = "SELECT * FROM $table_lots $where_sql $order_sql $limit_sql";

        if (!empty($where_values)) {
            $sql = $wpdb->prepare($sql, $where_values);
        }

        return $wpdb->get_results($sql);
    }

    /**
     * Get total count of lots with filters
     */
    public static function get_lots_count($args = array()) {
        global $wpdb;

        $table_lots = $wpdb->prefix . 'auction_lots';
        
        $defaults = array(
            'status' => '',
            'search' => '',
            'auction_house' => ''
        );

        $args = wp_parse_args($args, $defaults);

        $where_clauses = array();
        $where_values = array();

        if (!empty($args['status'])) {
            $where_clauses[] = "status = %s";
            $where_values[] = $args['status'];
        }

        if (!empty($args['search'])) {
            $where_clauses[] = "(lot_id LIKE %s OR model LIKE %s OR grade LIKE %s)";
            $search_term = '%' . $wpdb->esc_like($args['search']) . '%';
            $where_values[] = $search_term;
            $where_values[] = $search_term;
            $where_values[] = $search_term;
        }

        if (!empty($args['auction_house'])) {
            $where_clauses[] = "auction_house = %s";
            $where_values[] = $args['auction_house'];
        }

        $where_sql = '';
        if (!empty($where_clauses)) {
            $where_sql = 'WHERE ' . implode(' AND ', $where_clauses);
        }

        $sql = "SELECT COUNT(*) FROM $table_lots $where_sql";

        if (!empty($where_values)) {
            $sql = $wpdb->prepare($sql, $where_values);
        }

        return (int) $wpdb->get_var($sql);
    }

    /**
     * Get a single lot by lot_id
     */
    public static function get_lot($lot_id) {
        global $wpdb;

        $table_lots = $wpdb->prefix . 'auction_lots';
        
        return $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_lots WHERE lot_id = %s",
            $lot_id
        ));
    }

    /**
     * Get items for a lot
     */
    public static function get_lot_items($lot_id) {
        global $wpdb;

        $table_items = $wpdb->prefix . 'auction_items';
        
        return $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM $table_items WHERE lot_id = %s ORDER BY id",
            $lot_id
        ));
    }

    /**
     * Get bids for a lot
     */
    public static function get_lot_bids($lot_id, $status = '') {
        global $wpdb;

        $table_bids = $wpdb->prefix . 'auction_bids';
        
        $where_sql = "WHERE lot_id = %s";
        $where_values = array($lot_id);

        if (!empty($status)) {
            $where_sql .= " AND status = %s";
            $where_values[] = $status;
        }

        $sql = "SELECT * FROM $table_bids $where_sql ORDER BY bid_time DESC";
        
        return $wpdb->get_results($wpdb->prepare($sql, $where_values));
    }

    /**
     * Save auction lot data
     */
    public static function save_lot($lot_data) {
        global $wpdb;

        $table_lots = $wpdb->prefix . 'auction_lots';
        
        // Check if lot already exists
        $existing_lot = self::get_lot($lot_data['lot_id']);
        
        if ($existing_lot) {
            // Update existing lot
            $result = $wpdb->update(
                $table_lots,
                $lot_data,
                array('lot_id' => $lot_data['lot_id']),
                array('%s', '%s', '%s', '%s', '%s', '%s', '%s', '%d', '%s', '%f', '%s'),
                array('%s')
            );
            return $result !== false ? $existing_lot->id : false;
        } else {
            // Insert new lot
            $result = $wpdb->insert($table_lots, $lot_data);
            return $result !== false ? $wpdb->insert_id : false;
        }
    }

    /**
     * Save lot items
     */
    public static function save_lot_items($lot_id, $items) {
        global $wpdb;

        $table_items = $wpdb->prefix . 'auction_items';
        
        // First, delete existing items for this lot
        $wpdb->delete($table_items, array('lot_id' => $lot_id), array('%s'));
        
        $success_count = 0;
        
        foreach ($items as $item) {
            $item_data = array(
                'lot_id' => $lot_id,
                'quantity' => isset($item['quantity']) ? intval($item['quantity']) : 0,
                'manufacturer' => isset($item['manufacturer']) ? $item['manufacturer'] : '',
                'model' => isset($item['model']) ? $item['model'] : '',
                'description' => isset($item['description']) ? $item['description'] : '',
                'part_number' => isset($item['part_number']) ? $item['part_number'] : '',
                'category' => isset($item['category']) ? $item['category'] : '',
                'grade' => isset($item['grade']) ? $item['grade'] : '',
                'capacity' => isset($item['capacity']) ? $item['capacity'] : '',
                'color' => isset($item['color']) ? $item['color'] : '',
                'carrier' => isset($item['carrier']) ? $item['carrier'] : '',
                'additional_data' => isset($item['additional_data']) ? json_encode($item['additional_data']) : ''
            );
            
            $result = $wpdb->insert($table_items, $item_data);
            if ($result !== false) {
                $success_count++;
            }
        }
        
        return $success_count;
    }

    /**
     * Get bid count for a lot
     */
    public static function get_lot_bid_count($lot_id) {
        global $wpdb;

        $table_bids = $wpdb->prefix . 'auction_bids';
        
        return (int) $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $table_bids WHERE lot_id = %s",
            $lot_id
        ));
    }

    /**
     * Get highest bid amount for a lot
     */
    public static function get_highest_bid($lot_id) {
        global $wpdb;

        $table_bids = $wpdb->prefix . 'auction_bids';
        
        $highest_bid = $wpdb->get_var($wpdb->prepare(
            "SELECT MAX(bid_amount) FROM $table_bids WHERE lot_id = %s AND status IN ('pending', 'accepted')",
            $lot_id
        ));
        
        return $highest_bid ? (float) $highest_bid : null;
    }

    /**
     * Check if user has existing bid for a lot
     */
    public static function user_has_bid($lot_id, $user_email) {
        global $wpdb;

        $table_bids = $wpdb->prefix . 'auction_bids';
        
        return $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_bids WHERE lot_id = %s AND user_email = %s AND status = 'pending' ORDER BY bid_time DESC LIMIT 1",
            $lot_id,
            $user_email
        ));
    }

    /**
     * Get user's bid status for a specific lot
     */
    public static function get_user_bid_status($lot_id, $user_id) {
        global $wpdb;

        $table_bids = $wpdb->prefix . 'auction_bids';
        
        return $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_bids WHERE lot_id = %s AND user_id = %d ORDER BY bid_time DESC LIMIT 1",
            $lot_id,
            $user_id
        ));
    }

    /**
     * Check if user can place a bid for a lot
     */
    public static function user_can_bid($lot_id, $user_id) {
        $existing_bid = self::get_user_bid_status($lot_id, $user_id);
        
        // User can bid if:
        // 1. They have no existing bid
        // 2. Their previous bid was rejected
        if (!$existing_bid || $existing_bid->status === 'rejected') {
            return true;
        }
        
        return false;
    }

    /**
     * Get user bid statuses for multiple lots
     */
    public static function get_user_bid_statuses($lot_ids, $user_id) {
        if (empty($lot_ids) || !$user_id) {
            return array();
        }

        global $wpdb;
        $table_bids = $wpdb->prefix . 'auction_bids';
        
        $placeholders = implode(',', array_fill(0, count($lot_ids), '%s'));
        $query_params = $lot_ids;
        $query_params[] = $user_id;
        
        $sql = "SELECT lot_id, status, bid_amount, bid_time 
                FROM $table_bids 
                WHERE lot_id IN ($placeholders) AND user_id = %d 
                ORDER BY bid_time DESC";
        
        $results = $wpdb->get_results($wpdb->prepare($sql, $query_params));
        
        // Group by lot_id and return only the latest bid for each lot
        $statuses = array();
        foreach ($results as $result) {
            if (!isset($statuses[$result->lot_id])) {
                $statuses[$result->lot_id] = $result;
            }
        }
        
        return $statuses;
    }

    /**
     * Get a single bid by ID
     */
    public static function get_bid($bid_id) {
        global $wpdb;

        $table_bids = $wpdb->prefix . 'auction_bids';
        
        return $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_bids WHERE id = %d",
            $bid_id
        ));
    }

    /**
     * Get all bids with optional filters
     */
    public static function get_bids($args = array()) {
        global $wpdb;

        $table_bids = $wpdb->prefix . 'auction_bids';
        
        $defaults = array(
            'status' => '',
            'limit' => 20,
            'offset' => 0,
            'orderby' => 'bid_time',
            'order' => 'DESC',
            'lot_id' => ''
        );

        $args = wp_parse_args($args, $defaults);

        $where_clauses = array();
        $where_values = array();

        if (!empty($args['status'])) {
            $where_clauses[] = "status = %s";
            $where_values[] = $args['status'];
        }

        if (!empty($args['lot_id'])) {
            $where_clauses[] = "lot_id = %s";
            $where_values[] = $args['lot_id'];
        }

        $where_sql = '';
        if (!empty($where_clauses)) {
            $where_sql = 'WHERE ' . implode(' AND ', $where_clauses);
        }

        $order_sql = sprintf('ORDER BY %s %s', 
            sanitize_sql_orderby($args['orderby']), 
            $args['order'] === 'ASC' ? 'ASC' : 'DESC'
        );

        $limit_sql = '';
        if ($args['limit'] > 0) {
            $limit_sql = $wpdb->prepare('LIMIT %d OFFSET %d', $args['limit'], $args['offset']);
        }

        $sql = "SELECT * FROM $table_bids $where_sql $order_sql $limit_sql";

        if (!empty($where_values)) {
            $sql = $wpdb->prepare($sql, $where_values);
        }

        return $wpdb->get_results($sql);
    }

    /**
     * Insert a bid
     */
    public static function save_bid($bid_data) {
        global $wpdb;

        $table_bids = $wpdb->prefix . 'auction_bids';
        
        return $wpdb->insert($table_bids, $bid_data);
    }

    /**
     * Update bid status
     */
    public static function update_bid_status($bid_id, $status, $admin_notes = '', $order_id = null) {
        global $wpdb;

        $table_bids = $wpdb->prefix . 'auction_bids';
        
        $update_data = array(
            'status' => $status,
            'admin_notes' => $admin_notes
        );

        if ($order_id) {
            $update_data['woocommerce_order_id'] = $order_id;
        }

        return $wpdb->update(
            $table_bids,
            $update_data,
            array('id' => $bid_id),
            array('%s', '%s', '%d'),
            array('%d')
        );
    }

    /**
     * Get dashboard statistics
     */
    public static function get_dashboard_stats() {
        return USF_Statistics_Helper::get_dashboard_statistics();
    }

    /**
     * Drop all plugin tables (for uninstall)
     */
    public static function drop_tables() {
        global $wpdb;

        $tables = array(
            $wpdb->prefix . 'auction_lots',
            $wpdb->prefix . 'auction_items',
            $wpdb->prefix . 'auction_bids',
            $wpdb->prefix . 'auction_settings'
        );

        foreach ($tables as $table) {
            $wpdb->query("DROP TABLE IF EXISTS $table");
        }
    }
}
