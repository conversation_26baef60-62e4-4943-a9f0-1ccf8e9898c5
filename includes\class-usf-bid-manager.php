<?php

/**
 * Bid management functionality
 *
 * Handles bid submission, validation, and management
 */

class USF_Bid_Manager {

    /**
     * Submit a new bid
     */
    public static function submit_bid($bid_data) {
        // Validate bid data
        $validation_result = self::validate_bid($bid_data);
        if (!$validation_result['valid']) {
            return array(
                'success' => false,
                'errors' => $validation_result['errors']
            );
        }

        // Check if auction is still active
        $lot = USF_Database::get_lot($bid_data['lot_id']);
        if (!$lot || !USF_Auction_Manager::is_auction_active($lot)) {
            return array(
                'success' => false,
                'errors' => array('This auction is no longer active')
            );
        }

        // Check if user can bid (no pending bid or previous bid was rejected)
        if (isset($bid_data['user_id'])) {
            if (!USF_Database::user_can_bid($bid_data['lot_id'], $bid_data['user_id'])) {
                return array(
                    'success' => false,
                    'errors' => array('You already have a pending bid for this auction. Please wait for a decision before submitting a new bid.')
                );
            }
            
            // Get existing bid to check if this is an update
            $existing_bid = USF_Database::get_user_bid_status($bid_data['lot_id'], $bid_data['user_id']);
        } else {
            // Fallback to email check for backward compatibility
            $existing_bid = USF_Database::user_has_bid($bid_data['lot_id'], $bid_data['user_email']);
            if ($existing_bid) {
                return array(
                    'success' => false,
                    'errors' => array('You already have a pending bid for this auction. Please wait for a decision before submitting a new bid.')
                );
            }
        }
        
        if ($existing_bid && $existing_bid->status === 'rejected') {
            // Update rejected bid with new bid
            global $wpdb;
            $table_bids = $wpdb->prefix . 'auction_bids';
            
            $update_data = array(
                'bid_amount' => floatval($bid_data['bid_amount']),
                'bid_time' => current_time('mysql'),
                'status' => 'pending',
                'admin_notes' => '' // Clear previous admin notes
            );
            
            // Update user info if provided
            if (!empty($bid_data['user_name'])) {
                $update_data['user_name'] = sanitize_text_field($bid_data['user_name']);
            }
            if (!empty($bid_data['user_phone'])) {
                $update_data['user_phone'] = sanitize_text_field($bid_data['user_phone']);
            }
            
            $result = $wpdb->update(
                $table_bids,
                $update_data,
                array('id' => $existing_bid->id),
                array('%f', '%s', '%s', '%s', '%s', '%s'),
                array('%d')
            );

            $bid_id = $existing_bid->id;
            $is_update = true;
        } else {
            // Create new bid
            $db_bid_data = array(
                'lot_id' => sanitize_text_field($bid_data['lot_id']),
                'user_email' => sanitize_email($bid_data['user_email']),
                'bid_amount' => floatval($bid_data['bid_amount']),
                'status' => 'pending'
            );
            
            // Add optional fields if provided
            if (!empty($bid_data['user_name'])) {
                $db_bid_data['user_name'] = sanitize_text_field($bid_data['user_name']);
            }
            if (!empty($bid_data['user_phone'])) {
                $db_bid_data['user_phone'] = sanitize_text_field($bid_data['user_phone']);
            }
            if (isset($bid_data['user_id'])) {
                $db_bid_data['user_id'] = intval($bid_data['user_id']);
            }

            $result = USF_Database::save_bid($db_bid_data);
            global $wpdb;
            $bid_id = $wpdb->insert_id;
            $is_update = false;
        }

        if ($result === false) {
            return array(
                'success' => false,
                'errors' => array('Failed to save bid. Please try again.')
            );
        }

        // Send confirmation email to bidder
        USF_Email_Manager::send_bid_confirmation($bid_id);

        // Send notification email to admin
        USF_Email_Manager::send_admin_notification($bid_id);

        return array(
            'success' => true,
            'bid_id' => $bid_id,
            'is_update' => $is_update,
            'message' => $is_update ? 'Your bid has been updated successfully.' : 'Your bid has been submitted successfully.'
        );
    }

    /**
     * Validate bid data
     */
    public static function validate_bid($bid_data) {
        $errors = array();

        // Required fields - only lot_id, user_email, and bid_amount are truly required
        $required_fields = array('lot_id', 'user_email', 'bid_amount');
        
        foreach ($required_fields as $field) {
            if (empty($bid_data[$field])) {
                $errors[] = ucfirst(str_replace('_', ' ', $field)) . ' is required';
            }
        }

        // Validate email
        if (!empty($bid_data['user_email']) && !is_email($bid_data['user_email'])) {
            $errors[] = 'Please enter a valid email address';
        }

        // Validate phone number (basic validation) - only if provided
        if (!empty($bid_data['user_phone'])) {
            $phone = preg_replace('/[^\d]/', '', $bid_data['user_phone']);
            if (strlen($phone) < 10) {
                $errors[] = 'Please enter a valid phone number';
            }
        }

        // Validate bid amount
        if (!empty($bid_data['bid_amount'])) {
            if (!is_numeric($bid_data['bid_amount']) || $bid_data['bid_amount'] <= 0) {
                $errors[] = 'Bid amount must be a positive number';
            } else {
                // Check if bid meets minimum requirements
                $lot = USF_Database::get_lot($bid_data['lot_id']);
                if ($lot) {
                    // If user has a pending bid, new bid must be higher than current pending bid
                    if (!empty($bid_data['user_id'])) {
                        $user_pending_bid = USF_Database::get_user_pending_bid_amount($bid_data['lot_id'], $bid_data['user_id']);
                        if ($user_pending_bid && $bid_data['bid_amount'] <= $user_pending_bid) {
                            $errors[] = 'New bid amount must be higher than your current bid of $' . number_format($user_pending_bid, 2);
                        }
                    }

                    // Also check against minimum offer (for new bidders or if no pending bid)
                    if ($bid_data['bid_amount'] < $lot->min_offer) {
                        $errors[] = 'Bid amount must be at least $' . number_format($lot->min_offer, 2);
                    }
                }
            }
        }

        // Validate lot exists
        if (!empty($bid_data['lot_id'])) {
            $lot = USF_Database::get_lot($bid_data['lot_id']);
            if (!$lot) {
                $errors[] = 'Invalid auction lot';
            }
        }

        return array(
            'valid' => empty($errors),
            'errors' => $errors
        );
    }

    /**
     * Accept a bid
     */
    public static function accept_bid($bid_id, $admin_notes = '') {
        global $wpdb;

        // Get bid details
        $table_bids = $wpdb->prefix . 'auction_bids';
        $bid = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_bids WHERE id = %d",
            $bid_id
        ));

        if (!$bid) {
            return array(
                'success' => false,
                'message' => 'Bid not found'
            );
        }

        if ($bid->status !== 'pending') {
            return array(
                'success' => false,
                'message' => 'Bid has already been processed'
            );
        }

        // Create WooCommerce order
        $order_result = USF_WooCommerce::create_order_from_bid($bid);
        
        if (!$order_result['success']) {
            return array(
                'success' => false,
                'message' => 'Failed to create WooCommerce order: ' . $order_result['message']
            );
        }

        // Update bid status
        $result = USF_Database::update_bid_status(
            $bid_id, 
            'accepted', 
            $admin_notes, 
            $order_result['order_id']
        );

        if ($result === false) {
            return array(
                'success' => false,
                'message' => 'Failed to update bid status'
            );
        }

        // Send acceptance email
        USF_Email_Manager::send_bid_acceptance($bid_id);

        return array(
            'success' => true,
            'message' => 'Bid accepted successfully',
            'order_id' => $order_result['order_id']
        );
    }

    /**
     * Reject a bid
     */
    public static function reject_bid($bid_id, $admin_notes = '') {
        global $wpdb;

        // Get bid details
        $table_bids = $wpdb->prefix . 'auction_bids';
        $bid = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_bids WHERE id = %d",
            $bid_id
        ));

        if (!$bid) {
            return array(
                'success' => false,
                'message' => 'Bid not found'
            );
        }

        if ($bid->status !== 'pending') {
            return array(
                'success' => false,
                'message' => 'Bid has already been processed'
            );
        }

        // Update bid status
        $result = USF_Database::update_bid_status($bid_id, 'rejected', $admin_notes);

        if ($result === false) {
            return array(
                'success' => false,
                'message' => 'Failed to update bid status'
            );
        }

        // Send rejection email
        USF_Email_Manager::send_bid_rejection($bid_id);

        return array(
            'success' => true,
            'message' => 'Bid rejected successfully'
        );
    }

    /**
     * Get pending bids for admin review
     */
    public static function get_pending_bids($limit = 50, $offset = 0) {
        global $wpdb;

        $table_bids = $wpdb->prefix . 'auction_bids';
        $table_lots = $wpdb->prefix . 'auction_lots';

        $sql = "SELECT b.*, l.model, l.grade, l.auction_house, l.min_offer 
                FROM $table_bids b 
                LEFT JOIN $table_lots l ON b.lot_id = l.lot_id 
                WHERE b.status = 'pending' 
                ORDER BY b.bid_time DESC 
                LIMIT %d OFFSET %d";

        return $wpdb->get_results($wpdb->prepare($sql, $limit, $offset));
    }

    /**
     * Get all bids for a specific lot
     */
    public static function get_lot_bids($lot_id, $include_all_statuses = false) {
        global $wpdb;

        $table_bids = $wpdb->prefix . 'auction_bids';
        
        $where_sql = "WHERE lot_id = %s";
        $where_values = array($lot_id);

        if (!$include_all_statuses) {
            $where_sql .= " AND status = 'pending'";
        }

        $sql = "SELECT * FROM $table_bids $where_sql ORDER BY bid_time DESC";
        
        return $wpdb->get_results($wpdb->prepare($sql, $where_values));
    }

    /**
     * Get bid statistics
     */
    public static function get_bid_stats() {
        $bid_stats = USF_Statistics_Helper::get_bid_statistics();

        // Flatten the structure for backward compatibility
        $stats = array_merge($bid_stats['bids'], $bid_stats['revenue'], $bid_stats['activity']);

        return $stats;
    }

    /**
     * Bulk action on bids
     */
    public static function bulk_action($action, $bid_ids, $admin_notes = '') {
        if (empty($bid_ids) || !is_array($bid_ids)) {
            return array(
                'success' => false,
                'message' => 'No bids selected'
            );
        }

        $results = array(
            'success' => 0,
            'failed' => 0,
            'messages' => array()
        );

        foreach ($bid_ids as $bid_id) {
            $bid_id = intval($bid_id);
            
            if ($action === 'accept') {
                $result = self::accept_bid($bid_id, $admin_notes);
            } elseif ($action === 'reject') {
                $result = self::reject_bid($bid_id, $admin_notes);
            } else {
                $results['failed']++;
                $results['messages'][] = "Invalid action for bid ID $bid_id";
                continue;
            }

            if ($result['success']) {
                $results['success']++;
            } else {
                $results['failed']++;
                $results['messages'][] = "Bid ID $bid_id: " . $result['message'];
            }
        }

        return array(
            'success' => true,
            'results' => $results
        );
    }

    /**
     * Get bid details with lot information
     */
    public static function get_bid_details($bid_id) {
        global $wpdb;

        $table_bids = $wpdb->prefix . 'auction_bids';
        $table_lots = $wpdb->prefix . 'auction_lots';

        $sql = "SELECT b.*, l.model, l.grade, l.auction_house, l.min_offer, l.url, l.closing_time 
                FROM $table_bids b 
                LEFT JOIN $table_lots l ON b.lot_id = l.lot_id 
                WHERE b.id = %d";

        return $wpdb->get_row($wpdb->prepare($sql, $bid_id));
    }

    /**
     * Delete a bid
     */
    public static function delete_bid($bid_id) {
        global $wpdb;

        $table_bids = $wpdb->prefix . 'auction_bids';

        // Check if bid exists and is not accepted (to prevent deleting processed bids)
        $bid = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_bids WHERE id = %d",
            $bid_id
        ));

        if (!$bid) {
            return array(
                'success' => false,
                'message' => 'Bid not found'
            );
        }

        if ($bid->status === 'accepted' && !empty($bid->woocommerce_order_id)) {
            return array(
                'success' => false,
                'message' => 'Cannot delete accepted bid with associated order'
            );
        }

        $result = $wpdb->delete($table_bids, array('id' => $bid_id), array('%d'));

        if ($result === false) {
            return array(
                'success' => false,
                'message' => 'Failed to delete bid'
            );
        }

        return array(
            'success' => true,
            'message' => 'Bid deleted successfully'
        );
    }

    /**
     * Get top bidders
     */
    public static function get_top_bidders($limit = 10) {
        global $wpdb;

        $table_bids = $wpdb->prefix . 'auction_bids';

        $sql = "SELECT user_email, user_name, 
                COUNT(*) as total_bids,
                SUM(CASE WHEN status = 'accepted' THEN 1 ELSE 0 END) as accepted_bids,
                SUM(CASE WHEN status = 'accepted' THEN bid_amount ELSE 0 END) as total_spent
                FROM $table_bids 
                GROUP BY user_email, user_name 
                ORDER BY total_spent DESC, accepted_bids DESC 
                LIMIT %d";

        return $wpdb->get_results($wpdb->prepare($sql, $limit));
    }
}
