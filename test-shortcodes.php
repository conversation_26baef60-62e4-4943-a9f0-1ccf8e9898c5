<?php
/**
 * Test page for shortcodes
 * 
 * Add this to your WordPress site temporarily to test shortcode rendering
 * Access via: yoursite.com/test-shortcodes.php
 */

// Load WordPress
require_once('wp-config.php');

// Start output buffering to capture shortcode output
ob_start();

echo "<!DOCTYPE html>";
echo "<html>";
echo "<head>";
echo "<title>Shortcode Test</title>";
echo "<link rel='stylesheet' href='" . plugin_dir_url(__FILE__) . "assets/css/public.css'>";
echo "</head>";
echo "<body>";

echo "<h1>Shortcode Test Page</h1>";

// Check if user is logged in
if (!is_user_logged_in()) {
    echo "<p><strong>Not logged in.</strong> <a href='" . wp_login_url() . "'>Login</a> to see bid status features.</p>";
} else {
    $current_user = wp_get_current_user();
    echo "<p><strong>Logged in as:</strong> " . $current_user->display_name . " (ID: " . $current_user->ID . ")</p>";
}

echo "<h2>Testing [allauctions] Shortcode</h2>";
echo "<div style='border: 2px solid blue; padding: 10px;'>";

// Test allauctions shortcode
echo do_shortcode('[allauctions limit="3"]');

echo "</div>";

// Test single auction if lot_id is provided
if (isset($_GET['lot_id'])) {
    echo "<h2>Testing [singleauction] Shortcode</h2>";
    echo "<div style='border: 2px solid green; padding: 10px;'>";
    echo do_shortcode('[singleauction lot_id="' . sanitize_text_field($_GET['lot_id']) . '"]');
    echo "</div>";
} else {
    // Get first auction for testing
    $auctions = USF_Database::get_lots(array('limit' => 1));
    if (!empty($auctions)) {
        $first_auction = $auctions[0];
        echo "<h2>Testing [singleauction] Shortcode</h2>";
        echo "<p><a href='?lot_id=" . $first_auction->lot_id . "'>Test with Lot #" . $first_auction->lot_id . "</a></p>";
        echo "<div style='border: 2px solid green; padding: 10px;'>";
        echo do_shortcode('[singleauction lot_id="' . $first_auction->lot_id . '"]');
        echo "</div>";
    }
}

echo "<script src='" . plugin_dir_url(__FILE__) . "assets/js/public.js'></script>";
echo "</body>";
echo "</html>";

// Get the output and clean it
$output = ob_get_clean();
echo $output;
?>
